<!--pages/product/ai-recommend/ai-recommend.wxml-->
<view class="page-container">
  <view class="page-header">
    <text class="page-title">AI智能推荐</text>
    <text class="page-desc">基于您的需求，AI为您推荐最适合的产品</text>
  </view>

  <view class="form-container">
    <!-- 基本信息区域 -->
    <view class="section-container">
      <view class="section-header">
        <text class="section-title">基本信息</text>
      </view>
      
      <!-- 标题输入 -->
      <view class="form-item">
        <text class="form-label">需求描述</text>
        <input 
          class="title-input" 
          placeholder="请简要描述您的需求，如：想买一款拍照好的手机" 
          maxlength="50" 
          bindinput="inputTitle"
          value="{{title}}"
        ></input>
        <text class="input-counter">{{title.length}}/50</text>
      </view>
      
      <!-- 使用场景描述 -->
      <view class="form-item">
        <text class="form-label">使用场景描述（选填）</text>
        <textarea 
          class="scene-input" 
          placeholder="请描述您的使用场景，例如：日常通勤使用、家庭影音娱乐等" 
          maxlength="500" 
          bindinput="inputScene"
          value="{{scene}}"
        ></textarea>
        <text class="input-counter">{{scene.length}}/500</text>
      </view>
      
      <!-- 标签选择 -->
      <view class="form-item">
        <text class="form-label">产品类别<text class="required-tip">(必选)</text></text>
        <view class="tags-container">
          <view 
            class="tag-item {{tagStatus[tag] ? 'active' : ''}}" 
            wx:for="{{tagOptions}}" 
            wx:for-item="tag" 
            wx:key="*this"
            bindtap="selectTag"
            data-tag="{{tag}}"
            hover-class="tag-hover"
          >
            <text>{{tag}}</text>
            <text class="tag-check" wx:if="{{tagStatus[tag]}}">✓</text>
          </view>
        </view>
        <text class="tip-text">请选择1个产品类别</text>
      </view>
      
      <!-- 关键考量因素 -->
      <view class="form-item">
        <text class="form-label">关键考量因素（选填）</text>
        <textarea 
          class="factor-input" 
          placeholder="输入考量因素，如：续航、相机、性能等" 
          maxlength="200" 
          bindinput="inputKeyFactors"
          value="{{keyFactors}}"
        ></textarea>
        <text class="input-counter">{{keyFactors.length}}/200</text>
      </view>
      
      <!-- 预算区间 -->
      <view class="form-item">
        <text class="form-label">预算区间（选填）</text>
        <view class="budget-container">
          <input
            class="budget-input"
            type="number"
            placeholder="最低预算"
            bindinput="inputBudgetMin"
            value="{{budget.min}}"
          ></input>
          <text class="budget-separator">-</text>
          <input
            class="budget-input"
            type="number"
            placeholder="最高预算"
            bindinput="inputBudgetMax"
            value="{{budget.max}}"
          ></input>
          <text class="budget-currency">元</text>
        </view>
      </view>
    </view>

    <!-- 品牌偏好选择区域 -->
    <view class="section-container" wx:if="{{selectedCategory}}">
      <view class="section-header">
        <text class="section-title">品牌偏好</text>
        <text class="section-desc">选择您偏好的品牌，AI将优先推荐这些品牌的产品（可选）</text>
      </view>

      <!-- 选中的产品类别显示 -->
      <view class="selected-category-display">
        <text class="category-label">产品类别：</text>
        <text class="category-name">{{selectedCategory}}</text>
      </view>

      <!-- 品牌选择网格 -->
      <view class="brand-selection-area">
        <view class="brand-grid">
          <view class="brand-item {{brandStatus[brand] ? 'selected' : ''}}"
                wx:for="{{availableBrands}}" wx:key="*this" wx:for-item="brand"
                bindtap="onToggleBrand" data-brand="{{brand}}"
                hover-class="brand-hover">
            <text class="brand-text">{{brand}}</text>
            <text class="brand-check" wx:if="{{brandStatus[brand]}}">✓</text>
          </view>
        </view>

        <!-- 已选择品牌提示 -->
        <view class="selected-brands-tip" wx:if="{{selectedBrands.length > 0}}">
          <text class="tip-text">已选择 {{selectedBrands.length}} 个品牌</text>
        </view>

        <!-- 品牌选择提示 -->
        <view class="brand-tip">
          <text class="tip-icon">💡</text>
          <text class="tip-text">不选择品牌也可以获取推荐，AI将为您推荐该类别下的所有优质产品</text>
        </view>
      </view>
    </view>



    <!-- 获取推荐按钮 -->
    <view class="recommend-container">
      <button
        class="recommend-btn {{!canRecommend ? 'disabled' : ''}}"
        bindtap="getAiRecommendation"
        disabled="{{!canRecommend}}"
        hover-class="btn-hover"
      >
        <text class="btn-icon">🚀</text>
        <text>获取AI推荐</text>
      </button>
    </view>

  </view>



  <!-- 加载提示 -->
  <view class="loading-overlay {{loading || processing ? 'show' : ''}}" wx:if="{{loading || processing}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text" wx:if="{{loading}}">AI正在为您分析推荐...</text>
      <text class="loading-text" wx:if="{{processing}}">{{processingMessage}}</text>
      <text class="loading-tip">这通常需要5-15秒，请耐心等待</text>
    </view>
  </view>
</view>