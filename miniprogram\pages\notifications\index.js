/**
 * 通知页面
 */
const { notification, comment, question, answer } = require('../../utils/api');
const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    notifications: [],     // 通知列表
    loading: false,        // 加载状态
    refreshing: false,     // 下拉刷新状态
    loadingMore: false,    // 加载更多状态
    hasMore: true,         // 是否有更多数据
    page: 1,               // 当前页码
    limit: 20,             // 每页数量
    isLoggedIn: false,     // 登录状态
    empty: false,          // 是否为空
    lastUserId: '',        // 记录最后一次加载的用户ID
    
    // 回复相关数据
    showReplyModal: false, // 是否显示回复对话框
    currentAnswerId: null, // 当前回复的回答ID
    currentCommentId: null, // 当前回复的评论ID
    currentNotificationId: null, // 当前回复的通知ID
    currentReplyType: 'answer', // 当前回复类型：answer(回复回答)或comment(回复评论)
    replyContent: '',      // 回复内容
    isAnonymous: false,    // 是否匿名回复
    submitting: false      // 提交中状态
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('通知页面 onLoad');
    
    // 检查登录状态
    this.checkLoginStatus();
    
    // 监听新通知事件
    if (app.getEventChannel) {
      app.getEventChannel().on('newNotification', this.handleNewNotification.bind(this));
      
      // 监听登录状态变化
      app.getEventChannel().on('loginStatusChanged', this.handleLoginStatusChanged.bind(this));
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    console.log('通知页面 onShow');
    
    // 检查登录状态变化
    const isLoggedIn = this.checkLoginStatus();
    
    // 获取当前用户ID
    const currentUserId = this.getCurrentUserId();
    console.log('当前用户ID:', currentUserId, '上次用户ID:', this.data.lastUserId);
    
    // 用户切换或首次加载时，强制刷新数据
    if (isLoggedIn && (currentUserId !== this.data.lastUserId || this.data.notifications.length === 0)) {
      console.log('用户已切换或首次加载，强制刷新通知数据');
      // 清空现有通知数据
      this.setData({ 
        notifications: [],
        page: 1,
        hasMore: true,
        empty: false,
        lastUserId: currentUserId
      });
      
      // 加载通知
      this.loadNotifications();
    } else if (isLoggedIn) {
      // 从其他页面返回时，不再调用updateNotificationsStatus
      // 而是直接获取最新的未读计数，让TabBar角标保持同步
      const app = getApp();
      if (app && app.updateUnreadCount) {
        console.log('通知页面：从其他页面返回，更新未读计数');
        app.updateUnreadCount();
      }
    }

    // 通知应用当前页面已显示，用于更新TabBar角标
    if (app && app.onPageShow) {
      app.onPageShow('pages/notifications/index');
    }
  },
  
  /**
   * 检查登录状态
   */
  checkLoginStatus: function() {
    // 确保app全局对象初始化完成
    if (!app || !app.globalData) {
      console.error('app全局对象未初始化');
      return false;
    }
    
    const token = wx.getStorageSync('token');
    const userInfo = wx.getStorageSync('userInfo');
    
    console.log('检查登录状态:', {
      appLoginState: app.globalData.isLoggedIn,
      hasToken: !!token,
      hasUserInfo: !!userInfo
    });
    
    // 判断是否已登录
    const isLoggedIn = !!(app.globalData.isLoggedIn && token && userInfo);
    
    // 如果登录状态变化，则更新状态
    if (isLoggedIn !== this.data.isLoggedIn) {
      console.log('登录状态变化:', this.data.isLoggedIn, '->', isLoggedIn);
      this.setData({ isLoggedIn });
    }
    
    return isLoggedIn;
  },

  /**
   * 获取当前登录用户ID
   */
  getCurrentUserId: function() {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      return userInfo && userInfo._id ? userInfo._id : '';
    } catch (error) {
      console.error('获取用户ID失败:', error);
      return '';
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    if (!this.data.isLoggedIn) {
      wx.stopPullDownRefresh();
      return;
    }
    
    this.setData({ 
      refreshing: true,
      page: 1,
      hasMore: true
    });
    
    this.loadNotifications(true)
      .finally(() => {
        wx.stopPullDownRefresh();
        this.setData({ refreshing: false });
      });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    if (!this.data.isLoggedIn || !this.data.hasMore || this.data.loadingMore) {
      return;
    }
    
    this.loadMore();
  },

  /**
   * 加载通知列表
   * @param {Boolean} refresh 是否刷新
   */
  loadNotifications: function (refresh = false) {
    if (this.data.loading) return Promise.resolve();
    
    // 再次确认登录状态
    if (!this.checkLoginStatus()) {
      console.error('未登录状态下尝试加载通知');
      this.setData({ 
        loading: false,
        empty: true
      });
      return Promise.resolve();
    }
    
    this.setData({ loading: true });
    
    const { page, limit } = this.data;
    
    console.log('开始请求通知列表:', { page, limit });
    
    return notification.getNotifications({ page, limit })
      .then(res => {
        console.log('获取通知列表响应:', res);
        
        // 修改判断逻辑，更健壮地处理各种响应情况
        if (res && (res.success === true || res.code === 200 || (res.data && res.data.notifications))) {
          const newNotifications = res.data && res.data.notifications ? res.data.notifications : [];
          const pagination = res.data && res.data.pagination ? res.data.pagination : null;
          
          console.log('获取到的通知数据:', newNotifications);
          
          // 确保通知数据存在且为数组
          if (!Array.isArray(newNotifications)) {
            console.error('通知数据格式错误:', newNotifications);
            this.setData({ 
              empty: true,
              loading: false 
            });
            return;
          }
          
          // 格式化通知数据
          const formattedNotifications = this.formatNotifications(newNotifications);
          console.log('格式化后的通知数据:', formattedNotifications);
          
          this.setData({
            notifications: refresh ? formattedNotifications : formattedNotifications,
            hasMore: pagination && page < pagination.pages,
            empty: formattedNotifications.length === 0,
            lastUserId: this.getCurrentUserId() // 更新最后加载的用户ID
          });
          
          // 如果是第一页且有通知，标记为已读
          // if (page === 1 && formattedNotifications.length > 0) {
          //   this.markAllAsRead();
          // }
        } else {
          console.error('获取通知失败:', res);
          wx.showToast({
            title: (res && res.message) || '获取通知失败',
            icon: 'none'
          });
          this.setData({ empty: true });
        }
      })
      .catch(err => {
        console.error('获取通知失败:', err);
        wx.showToast({
          title: '获取通知失败，请重试',
          icon: 'none'
        });
        this.setData({ empty: true });
      })
      .finally(() => {
        this.setData({ loading: false });
      });
  },

  /**
   * 加载更多通知
   */
  loadMore: function () {
    if (this.data.loadingMore || !this.data.hasMore) return;
    
    this.setData({ 
      loadingMore: true,
      page: this.data.page + 1
    });
    
    const { page, limit } = this.data;
    
    notification.getNotifications({ page, limit })
      .then(res => {
        // 修改判断逻辑，保持与loadNotifications一致
        if (res && (res.success === true || res.code === 200 || (res.data && res.data.notifications))) {
          const newNotifications = res.data && res.data.notifications ? res.data.notifications : [];
          const pagination = res.data && res.data.pagination ? res.data.pagination : null;
          
          // 格式化新加载的通知
          const formattedNotifications = this.formatNotifications(newNotifications);
          
          this.setData({
            notifications: [...this.data.notifications, ...formattedNotifications],
            hasMore: pagination && page < pagination.pages
          });
        } else {
          console.error('加载更多失败:', res);
          wx.showToast({
            title: (res && res.message) || '加载更多失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('加载更多失败:', err);
        wx.showToast({
          title: '加载更多失败，请重试',
          icon: 'none'
        });
        
        // 恢复页码
        this.setData({ page: this.data.page - 1 });
      })
      .finally(() => {
        this.setData({ loadingMore: false });
      });
  },

  /**
   * 格式化通知数据
   * @param {Array} notifications 通知数据
   * @returns {Array} 格式化后的通知数据
   */
  formatNotifications: function (notifications) {
    if (!Array.isArray(notifications)) {
      console.error('通知数据不是数组:', notifications);
      return [];
    }
    
    if (notifications.length === 0) {
      console.log('没有通知数据可格式化');
      return [];
    }
    
    console.log('开始格式化通知数据, 数量:', notifications.length);
    
    return notifications.map((notification, index) => {
      console.log(`格式化第${index+1}条通知:`, notification);
      
      // 确保通知有必要的字段
      if (!notification || !notification._id) {
        console.error(`第${index+1}条通知数据无效:`, notification);
        return null;
      }
      
      if (!notification.createdAt) {
        console.error(`第${index+1}条通知缺少createdAt字段:`, notification);
        return {
          ...notification,
          timeText: '未知时间',
          iconClass: this.getNotificationIcon(notification.type || 'unknown')
        };
      }
      
      // 格式化时间
      const createdAt = new Date(notification.createdAt);
      const now = new Date();
      let timeText = '';
      
      const diffMinutes = Math.floor((now - createdAt) / (1000 * 60));
      const diffHours = Math.floor(diffMinutes / 60);
      const diffDays = Math.floor(diffHours / 24);
      
      if (diffMinutes < 1) {
        timeText = '刚刚';
      } else if (diffMinutes < 60) {
        timeText = `${diffMinutes}分钟前`;
      } else if (diffHours < 24) {
        timeText = `${diffHours}小时前`;
      } else if (diffDays < 30) {
        timeText = `${diffDays}天前`;
      } else {
        const year = createdAt.getFullYear();
        const month = createdAt.getMonth() + 1;
        const day = createdAt.getDate();
        timeText = `${year}-${month}-${day}`;
      }
      
      const result = {
        ...notification,
        timeText,
        // 根据类型设置图标
        iconClass: this.getNotificationIcon(notification.type || 'unknown')
      };
      
      console.log(`第${index+1}条通知格式化结果:`, result);
      return result;
    }).filter(item => item !== null); // 过滤掉无效的通知
  },

  /**
   * 根据通知类型获取图标类名
   * @param {String} type 通知类型
   * @returns {String} 图标类名
   */
  getNotificationIcon: function (type) {
    switch (type) {
      case 'question_voted':
        return 'icon-vote';
      case 'answer_commented':
        return 'icon-comment';
      case 'comment_replied':
        return 'icon-reply';
      case 'answer_liked':
        return 'icon-like';
      case 'product_comparison_completed':
        return 'icon-compare';
      default:
        return 'icon-notification';
    }
  },

  /**
   * 处理新通知
   * @param {Object} notification 新通知
   */
  handleNewNotification: function (notification) {
    console.log('通知页面收到新通知:', notification);

    // 检查是否已登录
    if (!this.data.isLoggedIn) {
      console.log('用户未登录，忽略新通知');
      return;
    }

    // 格式化新通知
    const formattedNotification = this.formatNotifications([notification])[0];

    // console.log('格式化后的通知:', formattedNotification);

    // 添加到通知列表头部
    this.setData({
      notifications: [formattedNotification, ...this.data.notifications],
      empty: false
    });

    console.log('通知列表已更新，当前通知数量:', this.data.notifications.length);
  },

  /**
   * 显示回复对话框
   * @param {Object} e 事件对象
   */
  showReplyModal: function(e) {
    // 不需要手动阻止冒泡，因为wxml中使用了catchtap
    // e.stopPropagation();
    
    const { id, answerid, commentid, replytype, needinfo } = e.currentTarget.dataset;
    
    // 如果需要先获取评论信息（针对comment_replied类型通知）
    if (needinfo && commentid) {
      wx.showLoading({ title: '获取评论信息...' });
      
      comment.getCommentInfo(commentid)
        .then(res => {
          if (res.success && res.data) {
            // 从评论信息中获取answerId
            const answerId = res.data.answerId;
            
            this.setData({
              showReplyModal: true,
              currentAnswerId: answerId,
              currentCommentId: commentid,
              currentNotificationId: id,
              currentReplyType: replytype || 'comment',
              replyContent: '',
              isAnonymous: false
            });
          } else {
            wx.showToast({
              title: '获取评论信息失败',
              icon: 'none'
            });
          }
        })
        .catch(err => {
          console.error('获取评论信息失败:', err);
          wx.showToast({
            title: '获取评论信息失败',
            icon: 'none'
          });
        })
        .finally(() => {
          wx.hideLoading();
        });
    } else {
      // 对于不需要额外获取信息的情况，直接打开回复框
      this.setData({
        showReplyModal: true,
        currentAnswerId: answerid,
        currentCommentId: commentid || null,
        currentNotificationId: id,
        currentReplyType: replytype || 'answer',
        replyContent: '',
        isAnonymous: false
      });
    }
  },
  
  /**
   * 关闭回复对话框
   */
  closeReplyModal: function() {
    this.setData({
      showReplyModal: false,
      currentAnswerId: null,
      currentCommentId: null,
      currentNotificationId: null,
      currentReplyType: 'answer',
      replyContent: ''
    });
  },
  
  /**
   * 输入回复内容
   * @param {Object} e 事件对象
   */
  inputReply: function(e) {
    this.setData({
      replyContent: e.detail.value
    });
  },
  
  /**
   * 切换匿名选项
   */
  toggleAnonymous: function() {
    this.setData({
      isAnonymous: !this.data.isAnonymous
    });
  },
  
  /**
   * 提交回复
   */
  submitReply: function() {
    const { 
      currentAnswerId, 
      currentCommentId, 
      replyContent, 
      isAnonymous, 
      submitting,
      currentReplyType,
      currentNotificationId
    } = this.data;
    
    // 防止重复提交
    if (submitting) return;
    
    // 验证必要参数
    if (!currentAnswerId) {
      wx.showToast({
        title: '回答ID不能为空',
        icon: 'none'
      });
      return;
    }
    
    // 验证回复内容
    if (!replyContent.trim()) {
      wx.showToast({
        title: '请输入回复内容',
        icon: 'none'
      });
      return;
    }
    
    // 构建回复数据
    const commentData = {
      content: replyContent.trim(),
      isAnonymous
    };
    
    // 如果是回复评论，添加parentId
    if (currentReplyType === 'comment' && currentCommentId) {
      commentData.parentId = currentCommentId;
    }
    
    // 设置提交中状态
    this.setData({ submitting: true });
    
    // 提交回复
    comment.createComment(currentAnswerId, commentData)
      .then(res => {
        if (res.success) {
          wx.showToast({
            title: '回复成功',
            icon: 'success'
          });
          
          // 关闭回复对话框并清空回复内容
          this.closeReplyModal();
          
          // 标记当前通知为已读
          if (currentNotificationId) {
            this.markNotificationRead(currentNotificationId);
          }
        } else {
          wx.showToast({
            title: res.message || '回复失败',
            icon: 'none'
          });
        }
      })
      .catch(err => {
        console.error('提交回复失败:', err);
        wx.showToast({
          title: '回复失败，请重试',
          icon: 'none'
        });
      })
      .finally(() => {
        // 重置提交中状态
        this.setData({ submitting: false });
      });
  },

  /**
   * 标记所有通知为已读
   */
  markAllAsRead: function () {
    if (this.data.notifications.length === 0) return;
    
    notification.markAllAsRead()
      .then(res => {
        if (res.success && res.code === 200) {
          // 更新未读状态
          const updatedNotifications = this.data.notifications.map(item => ({
            ...item,
            isRead: true
          }));
          
          this.setData({ notifications: updatedNotifications });
          
          // 更新未读通知计数
          if (app.updateUnreadCount) {
            app.updateUnreadCount();
          }
        }
      })
      .catch(err => {
        console.error('标记所有通知为已读失败:', err);
      });
  },

  /**
   * 标记单个通知为已读
   * @param {Object} e 事件对象
   */
  markAsRead: function (e) {
    const { id, index } = e.currentTarget.dataset;
    
    notification.markAsRead(id)
      .then(res => {
        if (res.success && res.code === 200) {
          // 更新通知状态
          const notifications = [...this.data.notifications];
          notifications[index].isRead = true;
          
          this.setData({ notifications });
          
          // 更新未读通知计数
          if (app && app.updateUnreadCount) {
            app.updateUnreadCount();
          }
        }
      })
      .catch(err => {
        console.error('标记通知为已读失败:', err);
      });
  },

  /**
   * 点击通知处理
   * @param {Object} e 事件对象
   */
  onNotificationTap: function (e) {
    const { id, index, itemid, itemtype } = e.currentTarget.dataset;
    
    // 获取当前通知项
    const notification = this.data.notifications[index];
    if (!notification) return;
    
    // 标记为已读（优化逻辑）
    if (!notification.isRead) {
      // 先在本地标记为已读，提高用户体验
      const notifications = [...this.data.notifications];
      notifications[index].isRead = true;
      this.setData({ notifications });
      
      // 后台异步标记为已读
      this.markAsRead(e);
    }
    
    // 根据通知类型跳转到相应页面
    switch (itemtype) {
      case 'question':
        this.handleQuestionNotification(notification, itemid, id);
        break;
      case 'answer':
        this.handleAnswerNotification(notification, itemid, id);
        break;
      case 'comment':
        this.handleCommentNotification(itemid, id);
        break;
      case 'product_comparison':
        this.handleProductComparisonNotification(notification, itemid, id);
        break;
    }
  },
  
  /**
   * 处理问题类型通知点击
   */
  handleQuestionNotification: function(notification, itemid, notificationId) {
    wx.showLoading({ title: '加载中...' });
    question.getQuestionById(itemid)
      .then(() => {
        wx.hideLoading();
        // 问题存在，继续跳转
        if (notification.type === 'question_voted' && 
            notification.metadata && 
            notification.metadata.hasReason && 
            notification.metadata.answerId) {
          // 如果是带理由的answer通知，跳转到问题详情页并定位到该回答
          wx.navigateTo({
            url: `/pages/question/detail/detail?id=${itemid}&answerId=${notification.metadata.answerId}&fromNotification=true`
          });
        } else {
          // 普通问题通知，直接跳转到问题详情页
          wx.navigateTo({
            url: `/pages/question/detail/detail?id=${itemid}&fromNotification=true`
          });
        }
      })
      .catch(err => {
        wx.hideLoading();
        // 判断是否是问题不存在错误
        if (err && err.code === 404) {
          wx.showToast({
            title: '该问题已被删除',
            icon: 'none',
            duration: 2000
          });
          // 标记通知已读，避免用户再次点击
          this.markNotificationRead(notificationId);
        } else {
          wx.showToast({
            title: '加载失败，请重试',
            icon: 'none'
          });
        }
      });
  },
  
  /**
   * 处理回答类型通知点击
   */
  handleAnswerNotification: function(notification, answerId, notificationId) {
    wx.showLoading({ title: '加载中...' });
    
    // 检查metadata是否包含评论ID或问题ID
    const commentId = notification.metadata && notification.metadata.commentId;
    const questionId = notification.metadata && notification.metadata.questionId;
    
    // 对于answer_liked类型的通知，可能在metadata中包含了questionId
    if (notification.type === 'answer_liked' && questionId) {
      this.navigateWithQuestionCheck(questionId, answerId, null, notificationId);
      return;
    }
    
    // 处理包含评论ID的情况
    if (commentId) {
      answer.getAnswerInfo(answerId)
        .then(res => {
          if (res.success && res.data) {
            const questionId = res.data.questionId;
            this.navigateWithQuestionCheck(questionId, answerId, commentId, notificationId);
          } else {
            wx.hideLoading();
            wx.showToast({ title: '加载失败', icon: 'none' });
          }
        })
        .catch(err => {
          wx.hideLoading();
          this.handleNavigationError(err, notificationId);
        });
    } else {
      // 没有评论ID，只定位到回答
      answer.getAnswerInfo(answerId)
        .then(res => {
          if (res.success && res.data) {
            const questionId = res.data.questionId;
            this.navigateWithQuestionCheck(questionId, answerId, null, notificationId);
          } else {
            wx.hideLoading();
            wx.showToast({ title: '加载失败', icon: 'none' });
          }
        })
        .catch(err => {
          wx.hideLoading();
          this.handleNavigationError(err, notificationId);
        });
    }
  },
  
  /**
   * 处理评论类型通知点击
   */
  handleCommentNotification: function(commentId, notificationId) {
    wx.showLoading({ title: '加载中...' });
    
    comment.getCommentInfo(commentId)
      .then(res => {
        if (res.success && res.data) {
          const questionId = res.data.questionId;
          const answerId = res.data.answerId;
          
          this.navigateWithQuestionCheck(questionId, answerId, commentId, notificationId);
        } else {
          wx.hideLoading();
          wx.showToast({ title: '加载失败', icon: 'none' });
        }
      })
      .catch(err => {
        wx.hideLoading();
        this.handleNavigationError(err, notificationId);
      });
  },
  
  /**
   * 先检查问题是否存在，再进行导航
   */
  navigateWithQuestionCheck: function(questionId, answerId, commentId, notificationId) {
    question.getQuestionById(questionId)
      .then(() => {
        // 构建URL
        let url = `/pages/question/detail/detail?id=${questionId}&answerId=${answerId}`;
        if (commentId) {
          url += `&commentId=${commentId}`;
        }
        url += '&fromNotification=true';
        
        // 导航到问题详情页
        wx.navigateTo({
          url,
          success: (res) => {
            // 设置事件通道
            const eventChannel = res.eventChannel;
            if (eventChannel) {
              eventChannel.on('markNotificationRead', () => {
                this.markNotificationRead(notificationId);
              });
            }
          }
        });
      })
      .catch(err => {
        this.handleNavigationError(err, notificationId);
      })
      .finally(() => {
        wx.hideLoading();
      });
  },
  
  /**
   * 处理导航过程中的错误
   */
  handleNavigationError: function(err, notificationId) {
    if (err && err.code === 404) {
      wx.showToast({
        title: '该问题已被删除',
        icon: 'none',
        duration: 2000
      });
      // 标记通知已读
      this.markNotificationRead(notificationId);
    } else {
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 前往登录页
   */
  goToLogin: function () {
    wx.navigateTo({
      url: '/pages/login/login'
    });
  },

  /**
   * 处理登录状态变化
   */
  handleLoginStatusChanged: function(isLoggedIn) {
    console.log('收到登录状态变化事件:', isLoggedIn);
    
    if (isLoggedIn) {
      // 新登录，清空通知列表并重新加载
      this.setData({
        notifications: [],
        page: 1,
        hasMore: true,
        empty: false,
        lastUserId: this.getCurrentUserId()
      });
      
      this.loadNotifications();
    } else {
      // 登出，清空通知列表
      this.setData({
        notifications: [],
        empty: true,
        lastUserId: ''
      });
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    console.log('通知页面 onUnload');
    // 解绑事件监听
    if (app.getEventChannel) {
      app.getEventChannel().off('newNotification');
      app.getEventChannel().off('loginStatusChanged');
    }
  },

  /**
   * 更新通知页面上的通知状态
   */
  updateNotificationsStatus: function() {
    // 由于后端没有提供已读通知ID列表API，这里改为使用本地状态
    // 当从通知详情页返回时，不再请求服务器，而是依赖本地已标记的已读状态
    console.log('通知页面：更新通知已读状态（本地）');
    
    // 可以在这里添加其他逻辑，例如从存储中获取已读通知ID等
    // 但目前我们依赖的是用户点击通知时在本地和服务器都已标记为已读的机制
  },

  /**
   * 根据通知ID标记为已读（供事件通道使用）
   * @param {String} notificationId 通知ID
   */
  markNotificationRead: function(notificationId) {
    if (!notificationId) return;
    
    notification.markAsRead(notificationId)
      .then(res => {
        if (res.success && res.code === 200) {
          // 更新未读通知计数
          if (app && app.updateUnreadCount) {
            app.updateUnreadCount();
          }
          
          // 更新本地通知状态
          const notifications = [...this.data.notifications];
          const index = notifications.findIndex(item => item._id === notificationId);
          if (index !== -1) {
            notifications[index].isRead = true;
            this.setData({ notifications });
          }
        }
      })
      .catch(err => {
        console.error('标记通知为已读失败:', err);
      });
  },

  /**
   * 处理产品对比通知点击
   */
  handleProductComparisonNotification: function(notification, itemid, notificationId) {
    console.log('处理产品对比通知点击:', notification);
    
    // 检查通知类型是否为产品对比完成
    if (notification.type !== 'product_comparison_completed') {
      console.warn('未识别的产品对比通知类型:', notification.type);
      wx.showToast({
        title: '通知类型错误',
        icon: 'none'
      });
      return;
    }
    
    // 从 metadata 中获取产品名称
    const productNames = notification.metadata && notification.metadata.productNames;
    
    if (!productNames || !Array.isArray(productNames) || productNames.length < 2) {
      console.error('产品对比通知缺少产品名称信息:', notification.metadata);
      wx.showToast({
        title: '对比产品信息不完整',
        icon: 'none'
      });
      return;
    }
    
    console.log('准备跳转到产品对比页面，产品名称:', productNames);
    
    // 标记通知为已读
    this.markNotificationRead(notificationId);
    
    // 跳转到产品对比页面
    wx.navigateTo({
      url: `/pages/product/product_compare_v4/product_compare_v4?productNames=${encodeURIComponent(JSON.stringify(productNames))}&fromNotification=true`,
      success: () => {
        console.log('成功跳转到产品对比页面');
      },
      fail: (err) => {
        console.error('跳转到产品对比页面失败:', err);
        wx.showToast({
          title: '页面跳转失败',
          icon: 'none'
        });
      }
    });
  },
});