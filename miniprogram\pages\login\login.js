const app = getApp();
const { auth } = require('../../utils/api');
const util = require('../../utils/util');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 控制是否显示手机号登录方式，true: 显示，false: 隐藏
    showPhoneLogin: false,
    phone: '',
    verifyCode: '',
    isPhoneValid: false,
    isCodeValid: false,
    countdownObj: null,
    countdownText: '获取验证码',
    countdownDisabled: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 检查是否已登录
    if (app.globalData.isLoggedIn) {
      wx.switchTab({
        url: '/pages/index/index'
      });
    }
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    // 清理倒计时
    if (this.data.countdownObj) {
      this.data.countdownObj.stop();
    }
  },

  /**
   * 处理手机号输入
   */
  onPhoneInput: function (e) {
    const phone = e.detail.value;
    const isPhoneValid = util.isValidPhone(phone);
    
    this.setData({
      phone,
      isPhoneValid
    });
  },

  /**
   * 处理验证码输入
   */
  onCodeInput: function (e) {
    const verifyCode = e.detail.value;
    const isCodeValid = util.isValidVerifyCode(verifyCode);
    
    this.setData({
      verifyCode,
      isCodeValid
    });
  },

  /**
   * 发送验证码
   */
  sendVerifyCode: function () {
    const { phone, countdownDisabled } = this.data;
    
    // 检查是否在倒计时中
    if (countdownDisabled) return;
    
    // 检查手机号格式
    if (!util.isValidPhone(phone)) {
      util.showToast('请输入正确的手机号');
      return;
    }
    
    // 显示加载提示
    util.showLoading('发送中...');
    
    // 调用发送验证码接口，使用auth类型支持统一认证
    auth.sendVerifyCode(phone, 'auth')
      .then(res => {
        util.hideLoading();
        util.showToast('验证码已发送', 'success');
        
        // 创建倒计时
        const countdownObj = util.countdown(60, (remainingTime) => {
          if (remainingTime > 0) {
            this.setData({
              countdownText: `${remainingTime}秒后重新获取`,
              countdownDisabled: true
            });
          } else {
            this.setData({
              countdownText: '获取验证码',
              countdownDisabled: false
            });
          }
        });
        
        // 开始倒计时
        countdownObj.start();
        
        this.setData({
          countdownObj
        });
      })
      .catch(err => {
        util.hideLoading();
        util.showToast(err.message || '发送失败，请稍后再试');
      });
  },

  /**
   * 统一认证（登录/注册）
   */
  phoneAuth: function () {
    const { phone, verifyCode, isPhoneValid, isCodeValid } = this.data;
    
    // 验证手机号和验证码
    if (!isPhoneValid) {
      util.showToast('请输入正确的手机号');
      return;
    }
    
    if (!isCodeValid) {
      util.showToast('请输入正确的验证码');
      return;
    }
    
    // 显示加载提示
    util.showLoading('验证中...');
    
    // 调用统一认证接口
    auth.phoneAuth(phone, verifyCode)
      .then(res => {
        util.hideLoading();
        
        // 统一处理登录/注册成功
        const message = res.data.isNewUser ? '注册成功' : '登录成功';
        util.showToast(message, 'success');
        
        // 保存登录信息
        app.login(res.data.user, res.data.token);
        
        // 跳转到首页
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/user/user'
          });
        }, 1500);
      })
      .catch(err => {
        util.hideLoading();
        util.showToast(err.message || '认证失败，请稍后再试');
      });
  },

  /**
   * 微信登录
   */
  wxLogin: function () {
    // 显示加载提示
    util.showLoading('登录中...');
    
    // 获取微信登录凭证
    wx.login({
      success: (result) => {
        if (result.code) {
          // 调用微信登录接口
          auth.wxLogin(result.code)
            .then(res => {
              util.hideLoading();
              util.showToast('登录成功', 'success');
              
              // 保存登录信息 (会同时处理WebSocket连接)
              app.login(res.data.user, res.data.token);
              
              // 跳转到首页
              setTimeout(() => {
                wx.switchTab({
                  url: '/pages/user/user'
                });
              }, 1500);
            })
            .catch(err => {
              util.hideLoading();
              util.showToast(err.message || '登录失败，请稍后再试');
            });
        } else {
          util.hideLoading();
          util.showToast('获取微信授权失败');
        }
      },
      fail: () => {
        util.hideLoading();
        util.showToast('微信登录失败，请稍后再试');
      }
    });
  }
}) 